import {
  PostTweetResult,
  TwitterOAuthConfig,
  TwitterOAuthService,
  TwitterTokens,
  TwitterUserInfo,
} from './auth';

/**
 * Twitter Manager for API operations
 */
export class TwitterManager {
  private config: TwitterOAuthConfig;

  constructor(config: TwitterOAuthConfig) {
    this.config = config;
  }

  /**
   * Get user information
   */
  async getUserInfo(tokens: TwitterTokens): Promise<TwitterUserInfo | null> {
    try {
      const response = await fetch('https://api.twitter.com/2/users/me', {
        headers: {
          Authorization: `Bearer ${tokens.accessToken}`,
          'User-Agent': this.config.clientId ? `TwitterApp/${this.config.clientId}` : 'TwitterApp',
        },
      });

      if (!response.ok) {
        console.error('Failed to get user info:', await response.text());
        return null;
      }

      const data = await response.json();

      return {
        id: data.data.id,
        username: data.data.username,
        name: data.data.name,
      };
    } catch (error) {
      console.error('Error getting user info:', error);
      return null;
    }
  }

  /**
   * Post a tweet
   */
  async postTweet(tokens: TwitterTokens, tweetData: any): Promise<PostTweetResult> {
    try {
      const response = await fetch('https://api.twitter.com/2/tweets', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${tokens.accessToken}`,
          'Content-Type': 'application/json',
          'User-Agent': this.config.clientId ? `TwitterApp/${this.config.clientId}` : 'TwitterApp',
        },
        body: JSON.stringify(tweetData),
      });

      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: `Twitter API error: ${response.status} - ${JSON.stringify(responseData)}`,
        };
      }

      return {
        success: true,
        tweetId: responseData.data.id,
        rateLimitStatus: {
          remaining: response.headers.get('x-rate-limit-remaining'),
          reset: response.headers.get('x-rate-limit-reset'),
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Refresh user tokens
   */
  async refreshUserTokens(refreshToken: string): Promise<TwitterTokens> {
    const oauthService = new TwitterOAuthService(this.config);
    return oauthService.refreshTokens(refreshToken);
  }
}
